import { useEffect, useRef } from 'react';
import { MessageItem } from './MessageItem';
import { MessageWithProfile, GuildMessageWithProfile } from './types';

interface MessageListProps {
  messages: (MessageWithProfile | GuildMessageWithProfile)[];
  currentUserId: string;
  isGuildChat?: boolean;
}

export function MessageList({ messages, currentUserId, isGuildChat = false }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="flex-1 overflow-y-auto min-h-0">
      <div className="p-4 space-y-4 flex flex-col">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <p className="text-white/50 text-sm">No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <MessageItem
              key={message.id}
              message={message}
              isCurrentUser={message.user_id === currentUserId}
              isGuildMessage={isGuildChat}
            />
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}
