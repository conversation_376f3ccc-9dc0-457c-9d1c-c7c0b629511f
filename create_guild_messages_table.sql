-- Create guild_messages table
-- This table is missing from the database and needs to be created for guild chat functionality

CREATE TABLE IF NOT EXISTS public.guild_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  guild_id UUID NOT NULL,
  user_id UUID NOT NULL,
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_edited BOOLEAN DEFAULT FALSE
);

-- Add foreign key constraints
ALTER TABLE public.guild_messages 
ADD CONSTRAINT guild_messages_guild_id_fkey 
FOREIGN KEY (guild_id) REFERENCES public.guilds(id) ON DELETE CASCADE;

ALTER TABLE public.guild_messages 
ADD CONSTRAINT guild_messages_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_guild_messages_guild_id ON public.guild_messages(guild_id);
CREATE INDEX IF NOT EXISTS idx_guild_messages_created_at ON public.guild_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_guild_messages_user_id ON public.guild_messages(user_id);

-- Enable RLS
ALTER TABLE public.guild_messages ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view guild messages if they are guild members" ON public.guild_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.guild_members 
      WHERE guild_members.guild_id = guild_messages.guild_id 
      AND guild_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert guild messages if they are guild members" ON public.guild_messages
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM public.guild_members 
      WHERE guild_members.guild_id = guild_messages.guild_id 
      AND guild_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own guild messages" ON public.guild_messages
  FOR UPDATE USING (auth.uid() = user_id);

-- Insert some test data for guild chat
INSERT INTO public.guild_messages (guild_id, user_id, content, message_type) VALUES
  (
    (SELECT id FROM public.guilds LIMIT 1),
    (SELECT id FROM public.profiles WHERE username = 'testuser' LIMIT 1),
    'Welcome to guild chat! This is a test message.',
    'user'
  ),
  (
    (SELECT id FROM public.guilds LIMIT 1),
    (SELECT id FROM public.profiles WHERE username = 'admin' LIMIT 1),
    'Guild chat is now working! 🎉',
    'user'
  );
